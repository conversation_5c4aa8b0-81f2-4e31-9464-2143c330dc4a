#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试布局是否正确
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_layout():
    """测试布局"""
    print("测试历史区间分析标签页布局...")
    
    # 创建主窗口
    root = tk.Tk()
    root.title("布局测试")
    root.geometry("1400x900")
    
    try:
        # 导入必要的模块
        from football_analysis_system.ui.tabs.historical_odds_interval_tab import HistoricalOddsIntervalTab
        from football_analysis_system.analysis.odds_analyzer import OddsAnalyzer
        from football_analysis_system.analysis.interval_analyzer import IntervalAnalyzer
        from football_analysis_system.config import DB_STANDARD_ODDS, DB_STRENGTH_MATCHUP, DB_MATCHES
        
        print("正在创建分析器...")
        # 创建分析器
        odds_analyzer = OddsAnalyzer(DB_STANDARD_ODDS)
        interval_analyzer = IntervalAnalyzer(DB_STRENGTH_MATCHUP)
        
        print("正在创建历史区间分析标签页...")
        # 创建历史区间分析标签页
        historical_tab = HistoricalOddsIntervalTab(
            root, 
            odds_analyzer, 
            interval_analyzer, 
            DB_MATCHES
        )
        historical_tab.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        print("检查是否有基本面分析区域...")
        # 检查是否有基本面分析区域
        if hasattr(historical_tab, 'fundamental_frame'):
            print("✓ 找到基本面分析框架")
            if hasattr(historical_tab, 'fundamental_text'):
                print("✓ 找到基本面分析文本区域")
            if hasattr(historical_tab, 'refresh_fundamental_btn'):
                print("✓ 找到获取基本面分析按钮")
            if hasattr(historical_tab, 'test_fundamental_btn'):
                print("✓ 找到测试按钮")
        else:
            print("✗ 没有找到基本面分析框架")
        
        # 添加说明
        info_label = tk.Label(root, text="如果右侧显示了'基本面分析结果'面板，说明布局正确", 
                             font=("Arial", 12), fg="#666666")
        info_label.pack(pady=5)
        
        print("布局测试界面已创建，请检查右侧是否有基本面分析结果面板...")
        root.mainloop()
        
    except Exception as e:
        print(f"创建测试界面时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_layout()
