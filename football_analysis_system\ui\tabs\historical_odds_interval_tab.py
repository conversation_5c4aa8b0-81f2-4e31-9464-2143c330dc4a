"""
历史赔率区间分析标签页

该模块实现历史赔率区间分析的用户界面，包括博彩公司选择、数据加载、
可视化图表展示等功能。
"""

import tkinter as tk
from tkinter import ttk, messagebox
import logging
import threading
from typing import Optional, List, Dict, Any
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk

from football_analysis_system.analysis.historical_data_processor import (
    HistoricalDataProcessor, HistoricalOddsRecord
)
from football_analysis_system.analysis.historical_database_interface import HistoricalDatabaseInterface
from football_analysis_system.analysis.historical_chart_visualizer import HistoricalChartVisualizer


class HistoricalOddsIntervalTab(tk.Frame):
    """历史赔率区间分析标签页"""
    
    def __init__(self, parent, odds_analyzer, interval_analyzer, db_path: str, match_id: str = None, team_db=None):
        """
        初始化历史赔率区间分析标签页

        Args:
            parent: 父容器
            odds_analyzer: 赔率分析器实例
            interval_analyzer: 区间分析器实例
            db_path: 数据库路径
            match_id: 当前比赛ID
            team_db: 球队数据库实例，用于计算档距差
        """
        super().__init__(parent, bg="#F5F5F5")

        # 保存参数
        self.odds_analyzer = odds_analyzer
        self.interval_analyzer = interval_analyzer
        self.db_path = db_path
        self.current_match_id = match_id
        self.team_db = team_db

        # 初始化日志
        self.logger = logging.getLogger(__name__)

        # 初始化组件
        self.db_interface = HistoricalDatabaseInterface(db_path)
        self.data_processor = HistoricalDataProcessor(odds_analyzer, interval_analyzer)
        self.chart_visualizer = None

        # 状态变量
        self.available_companies = []
        self.current_company_id = None
        self.current_analysis_results = []
        self.is_loading = False
        self.current_match_data = None  # 存储当前比赛数据
        
        # UI组件引用
        self.company_var = tk.StringVar()
        self.status_var = tk.StringVar(value="请选择博彩公司")
        self.progress_var = tk.DoubleVar()
        
        # 创建UI
        self._create_ui()
        
        # 初始化数据
        self._initialize_data()
    
    def _create_ui(self):
        """创建用户界面"""
        # 创建主容器
        main_container = tk.Frame(self, bg="#F5F5F5")
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建控制面板
        self._create_control_panel(main_container)
        
        # 创建图表区域
        self._create_chart_area(main_container)
        
        # 创建状态栏
        self._create_status_bar(main_container)
    
    def _create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = tk.LabelFrame(parent, text="控制面板", bg="#F5F5F5", 
                                     font=("Arial", 10, "bold"))
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 第一行：比赛信息和公司选择
        row1_frame = tk.Frame(control_frame, bg="#F5F5F5")
        row1_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 比赛ID显示
        tk.Label(row1_frame, text="比赛ID:", bg="#F5F5F5", 
                font=("Arial", 9)).pack(side=tk.LEFT)
        
        self.match_id_label = tk.Label(row1_frame, text=self.current_match_id or "未选择", 
                                      bg="#F5F5F5", font=("Arial", 9, "bold"),
                                      fg="#3366CC")
        self.match_id_label.pack(side=tk.LEFT, padx=(5, 20))
        
        # 博彩公司选择
        tk.Label(row1_frame, text="博彩公司:", bg="#F5F5F5", 
                font=("Arial", 9)).pack(side=tk.LEFT)
        
        self.company_combobox = ttk.Combobox(row1_frame, textvariable=self.company_var,
                                           state="readonly", width=20)
        self.company_combobox.pack(side=tk.LEFT, padx=(5, 10))
        self.company_combobox.bind("<<ComboboxSelected>>", self._on_company_selected)
        
        # 第二行：操作按钮
        row2_frame = tk.Frame(control_frame, bg="#F5F5F5")
        row2_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 分析按钮
        self.analyze_button = tk.Button(row2_frame, text="开始分析", 
                                       command=self._start_analysis,
                                       bg="#3366CC", fg="white", 
                                       font=("Arial", 9, "bold"),
                                       relief=tk.FLAT, padx=20)
        self.analyze_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 清除按钮
        self.clear_button = tk.Button(row2_frame, text="清除图表", 
                                     command=self._clear_chart,
                                     bg="#666666", fg="white", 
                                     font=("Arial", 9),
                                     relief=tk.FLAT, padx=20)
        self.clear_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 保存按钮
        self.save_button = tk.Button(row2_frame, text="保存图表", 
                                    command=self._save_chart,
                                    bg="#28A745", fg="white", 
                                    font=("Arial", 9),
                                    relief=tk.FLAT, padx=20)
        self.save_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 刷新按钮
        self.refresh_button = tk.Button(row2_frame, text="刷新数据", 
                                       command=self._refresh_data,
                                       bg="#FFC107", fg="black", 
                                       font=("Arial", 9),
                                       relief=tk.FLAT, padx=20)
        self.refresh_button.pack(side=tk.LEFT)
    
    def _create_chart_area(self, parent):
        """创建图表区域"""
        # 创建主要内容区域的水平布局
        content_frame = tk.Frame(parent, bg="#F5F5F5")
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 左侧：图表区域
        chart_frame = tk.LabelFrame(content_frame, text="历史赔率区间变化趋势",
                                   bg="#F5F5F5", font=("Arial", 10, "bold"))
        chart_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 创建图表容器，为双图表显示优化布局
        self.chart_container = tk.Frame(chart_frame, bg="white")
        self.chart_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 右侧：基本面分析结果显示区域
        self._create_fundamental_analysis_area(content_frame)

        # 初始化图表可视化器，设置更大的图形尺寸以适应双图表
        self.chart_visualizer = HistoricalChartVisualizer()
        # 调整图表尺寸以适应双图表显示
        self.chart_visualizer.figure_size = (10, 10)  # 调整宽度以适应右侧面板
        self.chart_visualizer.figure = Figure(figsize=self.chart_visualizer.figure_size,
                                            dpi=self.chart_visualizer.dpi)
        
        # 创建matplotlib画布
        self.canvas = self.chart_visualizer.create_canvas(self.chart_container)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 添加图表工具栏（可选）
        self._add_chart_toolbar()
        
        # 添加交互功能
        self.chart_visualizer.add_interactive_features()
        
        # 显示初始提示
        self._show_initial_message()

    def _create_fundamental_analysis_area(self, parent):
        """创建基本面分析结果显示区域"""
        # 基本面分析结果框架
        self.fundamental_frame = tk.LabelFrame(parent, text="基本面分析结果",
                                             bg="#F5F5F5", font=("Arial", 10, "bold"))
        self.fundamental_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0), ipadx=10)

        # 设置固定宽度
        self.fundamental_frame.config(width=350)
        self.fundamental_frame.pack_propagate(False)

        # 创建滚动文本区域
        text_frame = tk.Frame(self.fundamental_frame, bg="#F5F5F5")
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建文本框和滚动条
        self.fundamental_text = tk.Text(text_frame, wrap=tk.WORD,
                                       font=("Arial", 9), bg="white",
                                       relief=tk.FLAT, borderwidth=1)

        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL,
                                command=self.fundamental_text.yview)
        self.fundamental_text.config(yscrollcommand=scrollbar.set)

        # 布局文本框和滚动条
        self.fundamental_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 配置文本标签样式
        self.fundamental_text.tag_configure("title", font=("Arial", 10, "bold"),
                                          foreground="#2E86AB")
        self.fundamental_text.tag_configure("confidence", font=("Arial", 9, "bold"),
                                          foreground="#A23B72")
        self.fundamental_text.tag_configure("rule", font=("Arial", 8),
                                          foreground="#333333")
        self.fundamental_text.tag_configure("home", foreground="#D32F2F")
        self.fundamental_text.tag_configure("away", foreground="#1976D2")

        # 初始显示提示信息
        self._show_fundamental_initial_message()

        # 添加刷新按钮
        refresh_frame = tk.Frame(self.fundamental_frame, bg="#F5F5F5")
        refresh_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        self.refresh_fundamental_btn = tk.Button(refresh_frame, text="获取基本面分析",
                                               command=self._refresh_fundamental_analysis,
                                               bg="#28A745", fg="white",
                                               font=("Arial", 9, "bold"),
                                               relief=tk.FLAT, padx=15, pady=5)
        self.refresh_fundamental_btn.pack(fill=tk.X)

    def _show_fundamental_initial_message(self):
        """显示基本面分析区域的初始提示信息"""
        self.fundamental_text.delete(1.0, tk.END)
        self.fundamental_text.insert(tk.END, "基本面分析结果\n\n", "title")
        self.fundamental_text.insert(tk.END, "请先在基本面分析标签页完成分析，\n")
        self.fundamental_text.insert(tk.END, "然后点击下方按钮获取分析结果。\n\n")
        self.fundamental_text.insert(tk.END, "分析结果将显示：\n")
        self.fundamental_text.insert(tk.END, "• 主胜信心级别及合理区间\n")
        self.fundamental_text.insert(tk.END, "• 平局信心级别及合理区间\n")
        self.fundamental_text.insert(tk.END, "• 客胜信心级别及合理区间\n")
        self.fundamental_text.config(state=tk.DISABLED)

    def _add_chart_toolbar(self):
        """添加图表工具栏"""
        try:
            # 创建工具栏容器
            toolbar_frame = tk.Frame(self.chart_container, bg="white")
            toolbar_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))
            
            # 创建matplotlib导航工具栏
            self.toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame)
            self.toolbar.update()
            
            # 自定义工具栏样式
            self.toolbar.config(bg="white")
            
            # 添加自定义按钮
            self._add_custom_toolbar_buttons(toolbar_frame)
            
        except Exception as e:
            self.logger.warning(f"添加图表工具栏时出错: {e}")
    
    def _add_custom_toolbar_buttons(self, parent):
        """添加自定义工具栏按钮"""
        # 分隔符
        separator = tk.Frame(parent, width=2, bg="#CCCCCC")
        separator.pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 重置视图按钮
        reset_button = tk.Button(parent, text="重置视图", 
                                command=self._reset_chart_view,
                                bg="#F8F9FA", fg="#495057",
                                font=("Arial", 8), relief=tk.FLAT,
                                padx=10, pady=2)
        reset_button.pack(side=tk.LEFT, padx=2)
        
        # 全屏显示按钮
        fullscreen_button = tk.Button(parent, text="全屏显示", 
                                     command=self._toggle_fullscreen,
                                     bg="#F8F9FA", fg="#495057",
                                     font=("Arial", 8), relief=tk.FLAT,
                                     padx=10, pady=2)
        fullscreen_button.pack(side=tk.LEFT, padx=2)
        
        # 导出数据按钮
        export_button = tk.Button(parent, text="导出数据", 
                                 command=self._export_analysis_data,
                                 bg="#F8F9FA", fg="#495057",
                                 font=("Arial", 8), relief=tk.FLAT,
                                 padx=10, pady=2)
        export_button.pack(side=tk.LEFT, padx=2)
    
    def _reset_chart_view(self):
        """重置图表视图"""
        if hasattr(self.chart_visualizer, 'ax_main') and hasattr(self.chart_visualizer, 'ax_draw'):
            # 重置主客区间图视图
            self.chart_visualizer.ax_main.relim()
            self.chart_visualizer.ax_main.autoscale()
            
            # 重置平局区间图视图
            self.chart_visualizer.ax_draw.relim()
            self.chart_visualizer.ax_draw.autoscale()
            
            # 刷新画布
            if self.canvas:
                self.canvas.draw()
    
    def _toggle_fullscreen(self):
        """切换全屏显示"""
        # 这里可以实现全屏显示功能
        # 由于tkinter的限制，这里只是一个占位符
        messagebox.showinfo("提示", "全屏显示功能正在开发中")
    
    def _export_analysis_data(self):
        """导出分析数据"""
        if not self.current_analysis_results:
            messagebox.showwarning("警告", "没有可导出的数据")
            return
        
        from tkinter import filedialog
        
        # 选择保存路径
        filename = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")],
            title="导出分析数据"
        )
        
        if filename:
            success = self.export_analysis_data(filename)
            if success:
                messagebox.showinfo("成功", f"数据已导出到: {filename}")
            else:
                messagebox.showerror("错误", "导出失败")
    
    def _create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = tk.Frame(parent, bg="#E0E0E0", relief=tk.SUNKEN, bd=1)
        status_frame.pack(fill=tk.X)
        
        # 状态标签
        self.status_label = tk.Label(status_frame, textvariable=self.status_var,
                                    bg="#E0E0E0", font=("Arial", 9),
                                    anchor=tk.W)
        self.status_label.pack(side=tk.LEFT, padx=10, pady=2)
        
        # 进度条
        self.progress_bar = ttk.Progressbar(status_frame, variable=self.progress_var,
                                          mode='determinate', length=200)
        self.progress_bar.pack(side=tk.RIGHT, padx=10, pady=2)
    
    def _show_initial_message(self):
        """显示初始提示信息"""
        # 清除图表
        self.chart_visualizer.ax.clear()
        self.chart_visualizer.setup_chart_style()
        
        # 根据当前状态显示不同的提示信息
        if not self.current_match_id:
            message = "请先选择比赛"
        elif not self.available_companies:
            message = f"比赛 {self.current_match_id} 暂无历史赔率数据\n\n请选择其他比赛或等待数据更新"
        else:
            message = '请选择博彩公司并点击"开始分析"查看历史赔率区间变化趋势'
        
        # 在图表区域显示提示文本
        self.chart_visualizer.ax.text(0.5, 0.5, 
                                     message,
                                     horizontalalignment='center',
                                     verticalalignment='center',
                                     transform=self.chart_visualizer.ax.transAxes,
                                     fontsize=12, color='#666666',
                                     wrap=True)
        self.chart_visualizer.ax.set_xlim(0, 1)
        self.chart_visualizer.ax.set_ylim(0, 1)
        self.chart_visualizer.ax.set_xticks([])
        self.chart_visualizer.ax.set_yticks([])
        
        if self.canvas:
            self.canvas.draw()

    def _refresh_fundamental_analysis(self):
        """刷新基本面分析结果"""
        try:
            # 获取主应用实例
            root = self.winfo_toplevel()
            if hasattr(root, 'app') and hasattr(root.app, 'fundamental_analysis_tab'):
                # 获取基本面分析结果
                analysis_results = root.app.fundamental_analysis_tab.get_latest_analysis_results()

                if analysis_results:
                    self._display_fundamental_results(analysis_results)
                else:
                    self._show_no_analysis_message()
            else:
                self._show_no_analysis_message()

        except Exception as e:
            self.logger.error(f"刷新基本面分析结果时出错: {e}")
            self._show_error_message(str(e))

    def _display_fundamental_results(self, results):
        """显示基本面分析结果"""
        try:
            self.fundamental_text.config(state=tk.NORMAL)
            self.fundamental_text.delete(1.0, tk.END)

            # 标题
            self.fundamental_text.insert(tk.END, "基本面分析结果\n\n", "title")

            # 队伍信息
            teams = results.get('teams', {})
            home_team = teams.get('home_team', '主队')
            away_team = teams.get('away_team', '客队')
            league_name = teams.get('league_name', '')

            if league_name:
                self.fundamental_text.insert(tk.END, f"联赛：{league_name}\n")
            self.fundamental_text.insert(tk.END, f"对阵：", "rule")
            self.fundamental_text.insert(tk.END, f"{home_team}", "home")
            self.fundamental_text.insert(tk.END, " vs ", "rule")
            self.fundamental_text.insert(tk.END, f"{away_team}\n\n", "away")

            # 主胜分析
            home_win = results.get('home_win', {})
            self.fundamental_text.insert(tk.END, "主胜信心", "confidence")
            self.fundamental_text.insert(tk.END, f"：{home_win.get('confidence', '未知')}\n", "rule")
            self.fundamental_text.insert(tk.END, "合理赔率区间：\n", "rule")
            for i, rule in enumerate(home_win.get('odds_rules', []), 1):
                self.fundamental_text.insert(tk.END, f"- {rule}\n", "rule")
            self.fundamental_text.insert(tk.END, "\n")

            # 平局分析
            draw = results.get('draw', {})
            self.fundamental_text.insert(tk.END, "平局信心", "confidence")
            self.fundamental_text.insert(tk.END, f"：{draw.get('confidence', '未知')}\n", "rule")
            self.fundamental_text.insert(tk.END, "合理赔率区间：\n", "rule")
            for i, rule in enumerate(draw.get('odds_rules', []), 1):
                self.fundamental_text.insert(tk.END, f"- {rule}\n", "rule")
            self.fundamental_text.insert(tk.END, "\n")

            # 客胜分析
            away_win = results.get('away_win', {})
            self.fundamental_text.insert(tk.END, "客胜信心", "confidence")
            self.fundamental_text.insert(tk.END, f"：{away_win.get('confidence', '未知')}\n", "rule")
            self.fundamental_text.insert(tk.END, "合理赔率区间：\n", "rule")
            for i, rule in enumerate(away_win.get('odds_rules', []), 1):
                self.fundamental_text.insert(tk.END, f"- {rule}\n", "rule")

            # 添加更新时间
            import datetime
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.fundamental_text.insert(tk.END, f"\n更新时间：{current_time}", "rule")

            self.fundamental_text.config(state=tk.DISABLED)

        except Exception as e:
            self.logger.error(f"显示基本面分析结果时出错: {e}")
            self._show_error_message(str(e))

    def _show_no_analysis_message(self):
        """显示无分析结果的提示"""
        self.fundamental_text.config(state=tk.NORMAL)
        self.fundamental_text.delete(1.0, tk.END)
        self.fundamental_text.insert(tk.END, "基本面分析结果\n\n", "title")
        self.fundamental_text.insert(tk.END, "暂无基本面分析结果\n\n", "rule")
        self.fundamental_text.insert(tk.END, "请先到基本面分析标签页：\n", "rule")
        self.fundamental_text.insert(tk.END, "1. 选择比赛\n", "rule")
        self.fundamental_text.insert(tk.END, "2. 点击\"开始基本面分析\"\n", "rule")
        self.fundamental_text.insert(tk.END, "3. 等待分析完成\n", "rule")
        self.fundamental_text.insert(tk.END, "4. 返回此页面点击\"获取基本面分析\"\n", "rule")
        self.fundamental_text.config(state=tk.DISABLED)

    def _show_error_message(self, error_msg):
        """显示错误信息"""
        self.fundamental_text.config(state=tk.NORMAL)
        self.fundamental_text.delete(1.0, tk.END)
        self.fundamental_text.insert(tk.END, "基本面分析结果\n\n", "title")
        self.fundamental_text.insert(tk.END, "获取分析结果时出错：\n", "rule")
        self.fundamental_text.insert(tk.END, f"{error_msg}\n", "rule")
        self.fundamental_text.config(state=tk.DISABLED)

    def _initialize_data(self):
        """初始化数据"""
        if not self.current_match_id:
            self.status_var.set("未选择比赛")
            return
        
        # 在后台线程中加载可用公司
        threading.Thread(target=self._load_available_companies, daemon=True).start()
    
    def _load_available_companies(self):
        """加载可用的博彩公司"""
        try:
            self.status_var.set("正在加载博彩公司...")
            self.logger.info(f"开始加载比赛 {self.current_match_id} 的博彩公司数据")
            
            # 验证数据库连接
            if not self.db_interface.validate_database_connection():
                error_msg = "数据库连接失败"
                self.logger.error(error_msg)
                self.after(0, lambda: self.status_var.set(error_msg))
                return
            
            self.logger.info("数据库连接验证通过")
            
            # 获取可用公司
            companies = self.db_interface.get_available_companies(self.current_match_id)
            self.logger.info(f"从数据库获取到 {len(companies)} 家博彩公司")
            
            if not companies:
                error_msg = f"比赛 {self.current_match_id} 未找到历史赔率数据"
                self.logger.warning(error_msg)
                self.after(0, lambda: self.status_var.set(error_msg))
                
                # 检查数据库中是否有任何历史数据
                self._check_database_status()
                return
            
            self.available_companies = companies
            self.logger.info(f"成功加载博彩公司: {[c['company_name'] for c in companies]}")
            
            # 更新UI（在主线程中执行）
            self.after(0, self._update_company_combobox)
            
        except Exception as e:
            error_msg = f"加载博彩公司时出错: {str(e)}"
            self.logger.error(error_msg)
            import traceback
            self.logger.error(traceback.format_exc())
            self.after(0, lambda: self.status_var.set(error_msg))
    
    def _check_database_status(self):
        """检查数据库状态并提供调试信息"""
        try:
            self.logger.info("开始检查数据库状态...")
            
            # 检查history_odds表是否存在
            from football_analysis_system.db.database_manager import DatabaseManager
            db_manager = DatabaseManager(self.db_path)
            
            tables = db_manager.get_table_names()
            self.logger.info(f"数据库中的表: {tables}")
            
            if 'history_odds' not in tables:
                self.logger.error("history_odds表不存在")
                self.after(0, lambda: self.status_var.set("错误: history_odds表不存在"))
                return
            
            # 检查表中的数据量
            result = db_manager.execute_query("SELECT COUNT(*) FROM history_odds", fetchall=False)
            total_count = result[0] if result else 0
            self.logger.info(f"history_odds表总记录数: {total_count}")
            
            if total_count == 0:
                self.after(0, lambda: self.status_var.set("错误: history_odds表为空，请先运行数据抓取"))
                return
            
            # 检查有多少个不同的比赛
            result = db_manager.execute_query("SELECT COUNT(DISTINCT match_id) FROM history_odds", fetchall=False)
            match_count = result[0] if result else 0
            self.logger.info(f"有历史数据的比赛数量: {match_count}")
            
            # 显示一些示例比赛ID
            sample_matches = db_manager.execute_query("SELECT DISTINCT match_id FROM history_odds LIMIT 5")
            if sample_matches:
                sample_ids = [row[0] for row in sample_matches]
                self.logger.info(f"示例比赛ID: {sample_ids}")
                
                # 检查当前比赛ID是否在数据库中
                if self.current_match_id not in sample_ids:
                    self.logger.warning(f"当前比赛ID {self.current_match_id} 不在历史数据中")
                    available_matches_str = ', '.join(sample_ids[:3])
                    if len(sample_ids) > 3:
                        available_matches_str += f" 等{len(sample_ids)}场比赛"
                    self.after(0, lambda: self.status_var.set(f"当前比赛无历史数据。有历史数据的比赛: {available_matches_str}"))
                    # 同时更新图表提示信息
                    self.after(0, self._show_initial_message)
                else:
                    self.logger.info(f"当前比赛ID {self.current_match_id} 在历史数据中")
            
        except Exception as e:
            self.logger.error(f"检查数据库状态时出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
    
    def _update_company_combobox(self):
        """更新博彩公司下拉框"""
        if not self.available_companies:
            return
        
        # 准备下拉框选项
        company_options = []
        for company in self.available_companies:
            option = f"{company['company_name']} (ID: {company['company_id']})"
            company_options.append(option)
        
        # 更新下拉框
        self.company_combobox['values'] = company_options
        
        # 默认选择第一个公司
        if company_options:
            self.company_combobox.current(0)
            self.company_var.set(company_options[0])
            self._extract_company_id_from_selection()
        
        self.status_var.set(f"已加载 {len(company_options)} 家博彩公司")
    
    def _extract_company_id_from_selection(self):
        """从选择的文本中提取公司ID"""
        selection = self.company_var.get()
        if not selection:
            return
        
        # 从 "公司名称 (ID: 123)" 格式中提取ID
        try:
            start = selection.rfind("ID: ") + 4
            end = selection.rfind(")")
            if start > 3 and end > start:
                self.current_company_id = selection[start:end]
            else:
                # 备用方法：查找匹配的公司
                for company in self.available_companies:
                    if company['company_name'] in selection:
                        self.current_company_id = company['company_id']
                        break
        except Exception as e:
            self.logger.error(f"提取公司ID时出错: {e}")
    
    def _on_company_selected(self, event=None):
        """博彩公司选择事件处理"""
        self._extract_company_id_from_selection()
        
        if self.current_company_id:
            # 显示公司的历史记录数量
            count = self.db_interface.get_company_history_count(
                self.current_match_id, self.current_company_id
            )
            time_range = self.db_interface.get_time_range(
                self.current_match_id, self.current_company_id
            )
            
            status_msg = f"已选择公司，历史记录: {count} 条"
            if time_range[0] and time_range[1]:
                status_msg += f"，时间范围: {time_range[0]} 至 {time_range[1]}"
            
            self.status_var.set(status_msg)
    
    def _start_analysis(self):
        """开始分析"""
        if not self.current_match_id:
            messagebox.showwarning("警告", "请先选择比赛")
            return
        
        if not self.current_company_id:
            messagebox.showwarning("警告", "请先选择博彩公司")
            return
        
        if self.is_loading:
            messagebox.showinfo("提示", "分析正在进行中，请稍候...")
            return
        
        # 在后台线程中执行分析
        threading.Thread(target=self._perform_analysis, daemon=True).start()
    
    def _perform_analysis(self):
        """执行分析（后台线程）"""
        try:
            self.is_loading = True
            self.after(0, lambda: self._set_loading_state(True))
            
            # 步骤1：获取历史数据
            self.after(0, lambda: self.status_var.set("正在获取历史赔率数据..."))
            self.after(0, lambda: self.progress_var.set(20))
            
            historical_data = self.db_interface.get_historical_odds(
                self.current_match_id, self.current_company_id
            )
            
            if not historical_data:
                self.after(0, lambda: messagebox.showwarning("警告", "未找到历史数据"))
                return
            
            # 步骤2：处理数据
            self.after(0, lambda: self.status_var.set("正在处理历史数据..."))
            self.after(0, lambda: self.progress_var.set(50))
            
            # 计算档距差 - 使用公司特定的档距差
            gap_difference = self._get_company_gap_difference(self.current_company_id)
            self.logger.info(f"计算得到的档距差 (公司 {self.current_company_id}): {gap_difference}")

            analysis_results, visualization_data = self.data_processor.process_and_visualize_historical_data(
                historical_data, gap_difference
            )
            
            if not analysis_results:
                self.after(0, lambda: messagebox.showwarning("警告", "数据处理失败"))
                return
            
            # 步骤3：更新图表
            self.after(0, lambda: self.status_var.set("正在生成图表..."))
            self.after(0, lambda: self.progress_var.set(80))
            
            # 获取公司名称
            company_name = "未知公司"
            for company in self.available_companies:
                if company['company_id'] == self.current_company_id:
                    company_name = company['company_name']
                    break
            
            # 在主线程中更新图表
            self.after(0, lambda: self._update_chart(visualization_data, company_name))
            
            # 保存分析结果
            self.current_analysis_results = analysis_results
            
            # 完成
            self.after(0, lambda: self.status_var.set(f"分析完成，共处理 {len(analysis_results)} 条记录"))
            self.after(0, lambda: self.progress_var.set(100))
            
        except Exception as e:
            self.logger.error(f"分析过程中出错: {e}")
            self.after(0, lambda: messagebox.showerror("错误", f"分析失败: {str(e)}"))
            self.after(0, lambda: self.status_var.set("分析失败"))
        finally:
            self.is_loading = False
            self.after(0, lambda: self._set_loading_state(False))
            self.after(0, lambda: self.progress_var.set(0))
    
    def _update_chart(self, visualization_data, company_name):
        """更新图表（主线程）"""
        if not visualization_data:
            return
        
        success = self.chart_visualizer.plot_historical_intervals(
            visualization_data, company_name
        )
        
        if not success:
            messagebox.showerror("错误", "图表绘制失败")
    
    def _set_loading_state(self, loading: bool):
        """设置加载状态"""
        state = tk.DISABLED if loading else tk.NORMAL
        
        self.analyze_button.config(state=state)
        self.company_combobox.config(state="disabled" if loading else "readonly")
        self.clear_button.config(state=state)
        self.save_button.config(state=state)
        self.refresh_button.config(state=state)
    
    def _clear_chart(self):
        """清除图表"""
        self.chart_visualizer.clear_chart()
        self.current_analysis_results = []
        self.status_var.set("图表已清除")
        self._show_initial_message()
    
    def _save_chart(self):
        """保存图表"""
        if not self.current_analysis_results:
            messagebox.showwarning("警告", "没有可保存的图表")
            return
        
        from tkinter import filedialog
        
        # 选择保存路径
        filename = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG图片", "*.png"), ("所有文件", "*.*")],
            title="保存图表"
        )
        
        if filename:
            success = self.chart_visualizer.save_chart(filename)
            if success:
                messagebox.showinfo("成功", f"图表已保存到: {filename}")
            else:
                messagebox.showerror("错误", "保存失败")
    
    def _refresh_data(self):
        """刷新数据"""
        self.available_companies = []
        self.current_company_id = None
        self.company_combobox['values'] = []
        self.company_var.set("")
        
        self._clear_chart()
        self._initialize_data()
    
    def set_match_id(self, match_id: str):
        """设置当前比赛ID"""
        if match_id != self.current_match_id:
            self.current_match_id = match_id
            self.match_id_label.config(text=match_id or "未选择")
            self._refresh_data()

    def set_match_data(self, match_data):
        """设置当前比赛数据"""
        self.current_match_data = match_data
        if match_data and hasattr(match_data, 'match_id'):
            self.set_match_id(match_data.match_id)

    def _calculate_gap_difference(self):
        """计算档距差（保持向后兼容的默认方法）"""
        try:
            # 如果没有team_db或比赛数据，返回默认值
            if not self.team_db or not self.current_match_data:
                self.logger.warning("缺少team_db或比赛数据，使用默认档距差0.0")
                return 0.0

            # 获取主客队名称
            home_team = getattr(self.current_match_data, 'home_team', None)
            away_team = getattr(self.current_match_data, 'away_team', None)

            if not home_team or not away_team:
                self.logger.warning("无法获取主客队名称，使用默认档距差0.0")
                return 0.0

            # 计算档距差
            gap_difference = self.team_db.calculate_strength_gap(home_team, away_team)

            if gap_difference is None:
                self.logger.warning(f"无法计算档距差 {home_team} vs {away_team}，使用默认值0.0")
                return 0.0

            self.logger.info(f"成功计算档距差: {home_team} vs {away_team} = {gap_difference}")
            return gap_difference

        except Exception as e:
            self.logger.error(f"计算档距差时出错: {e}")
            return 0.0

    def _get_company_gap_difference(self, company_id):
        """获取指定公司的档距差"""
        try:
            # 如果没有team_db或比赛数据，返回默认值
            if not self.team_db or not self.current_match_data:
                return 0.0

            # 获取主客队数据
            home_team = getattr(self.current_match_data, 'home_team', None)
            away_team = getattr(self.current_match_data, 'away_team', None)

            if not home_team or not away_team:
                return 0.0

            # 获取主客队对象
            home_team_obj = self.team_db.get_team_data(home_team)
            away_team_obj = self.team_db.get_team_data(away_team)

            if not home_team_obj or not away_team_obj:
                return self._calculate_gap_difference()  # 回退到默认方法

            # 获取多公司评分数据
            home_ratings = getattr(home_team_obj, 'multi_company_ratings', {})
            away_ratings = getattr(away_team_obj, 'multi_company_ratings', {})

            # 查找指定公司的评分
            home_data = home_ratings.get(company_id, {})
            away_data = away_ratings.get(company_id, {})

            home_rating = home_data.get('rating')
            away_rating = away_data.get('rating')

            if home_rating is not None and away_rating is not None:
                try:
                    h_rating = float(home_rating)
                    a_rating = float(away_rating)
                    company_gap = round(h_rating - a_rating, 2)
                    self.logger.info(f"公司 {company_id} 的档距差: {company_gap} (主队评分: {h_rating}, 客队评分: {a_rating})")
                    return company_gap
                except (ValueError, TypeError):
                    pass

            # 如果没有找到公司特定的档距差，使用默认方法
            self.logger.warning(f"公司 {company_id} 没有多公司评分数据，使用默认档距差")
            return self._calculate_gap_difference()

        except Exception as e:
            self.logger.error(f"获取公司 {company_id} 档距差时出错: {e}")
            return self._calculate_gap_difference()

    def get_analysis_results(self) -> List[Any]:
        """获取当前分析结果"""
        return self.current_analysis_results.copy()
    
    def export_analysis_data(self, filepath: str) -> bool:
        """导出分析数据到文件"""
        if not self.current_analysis_results:
            return False
        
        try:
            import csv
            
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入标题行
                writer.writerow([
                    '时间', '主胜区间', '平局区间', '客胜区间',
                    '主胜规则值', '客胜规则值', '返还率',
                    '主胜满水赔率', '平局满水赔率', '客胜满水赔率'
                ])
                
                # 写入数据行
                for result in self.current_analysis_results:
                    writer.writerow([
                        result.timestamp,
                        result.home_interval,
                        result.draw_interval,
                        result.away_interval,
                        result.home_rule_value,
                        result.away_rule_value,
                        result.payout_rate,
                        result.home_true_odds,
                        result.draw_true_odds,
                        result.away_true_odds
                    ])
            
            return True
            
        except Exception as e:
            self.logger.error(f"导出数据时出错: {e}")
            return False