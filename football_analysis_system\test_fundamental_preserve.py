#!/usr/bin/env python3
"""
测试基本面分析页内容保持功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_preserve_logic():
    """测试内容保持逻辑"""
    print("=== 测试基本面分析页内容保持逻辑 ===")
    
    try:
        # 导入相关模块
        from ui.tabs.fundamental_analysis_tab import FundamentalAnalysisTab
        import tkinter as tk
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 创建基本面分析标签页
        tab = FundamentalAnalysisTab(root)
        
        # 模拟比赛数据
        match_data_1 = {
            'home_team': '曼城',
            'away_team': '利物浦',
            'league_name': '英超',
            'match_id': '12345'
        }
        
        match_data_2 = {
            'home_team': '皇马',
            'away_team': '巴萨',
            'league_name': '西甲',
            'match_id': '67890'
        }
        
        print("1. 测试首次选择比赛（应该清空结果）...")
        tab.update_match_info(match_data_1, is_tab_switch=False)
        
        # 检查比赛信息是否正确设置
        if (tab.home_team == '曼城' and 
            tab.away_team == '利物浦' and 
            tab.league_name == '英超'):
            print("✅ 比赛信息设置正确")
        else:
            print("❌ 比赛信息设置错误")
            return False
        
        # 模拟添加一些分析结果
        print("2. 模拟添加分析结果...")
        tab.ui.append_result("这是模拟的分析结果内容")
        
        # 检查结果是否存在
        result_content = tab.ui.result_text.get("1.0", "end-1c")
        if "模拟的分析结果内容" in result_content:
            print("✅ 分析结果已添加")
        else:
            print("❌ 分析结果添加失败")
            return False
        
        print("3. 测试标签页切换（同一场比赛，应该保持结果）...")
        tab.update_match_info(match_data_1, is_tab_switch=True)
        
        # 检查结果是否保持
        result_content_after_switch = tab.ui.result_text.get("1.0", "end-1c")
        if "模拟的分析结果内容" in result_content_after_switch:
            print("✅ 标签页切换后结果保持正常")
        else:
            print("❌ 标签页切换后结果被清空")
            return False
        
        print("4. 测试选择新比赛（应该清空结果）...")
        tab.update_match_info(match_data_2, is_tab_switch=False)
        
        # 检查结果是否被清空
        result_content_after_new_match = tab.ui.result_text.get("1.0", "end-1c")
        if "模拟的分析结果内容" not in result_content_after_new_match:
            print("✅ 选择新比赛后结果正确清空")
        else:
            print("❌ 选择新比赛后结果未清空")
            return False
        
        # 检查新比赛信息是否正确设置
        if (tab.home_team == '皇马' and 
            tab.away_team == '巴萨' and 
            tab.league_name == '西甲'):
            print("✅ 新比赛信息设置正确")
        else:
            print("❌ 新比赛信息设置错误")
            return False
        
        print("5. 测试标签页切换到不同比赛（应该清空结果）...")
        # 先添加一些结果
        tab.ui.append_result("新比赛的分析结果")
        
        # 然后切换到第一场比赛（标签页切换但比赛不同）
        tab.update_match_info(match_data_1, is_tab_switch=True)
        
        # 检查结果是否被清空（因为比赛不同）
        result_content_different_match = tab.ui.result_text.get("1.0", "end-1c")
        if "新比赛的分析结果" not in result_content_different_match:
            print("✅ 标签页切换到不同比赛时结果正确清空")
        else:
            print("❌ 标签页切换到不同比赛时结果未清空")
            return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_preserve_parameter():
    """测试UI层面的preserve_results参数"""
    print("\n=== 测试UI层面的preserve_results参数 ===")
    
    try:
        from ui.tabs.fundamental_analysis_ui import FundamentalAnalysisUI
        import tkinter as tk
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()
        
        # 创建UI实例
        ui = FundamentalAnalysisUI(root)
        ui.create_widgets()
        
        # 添加一些测试内容
        ui.append_result("测试内容保持")
        
        # 检查内容是否存在
        content_before = ui.result_text.get("1.0", "end-1c")
        if "测试内容保持" in content_before:
            print("✅ 测试内容已添加")
        else:
            print("❌ 测试内容添加失败")
            return False
        
        # 测试preserve_results=True
        print("测试preserve_results=True...")
        ui.update_match_info("英超", "曼城", "利物浦", preserve_results=True)
        
        content_after_preserve = ui.result_text.get("1.0", "end-1c")
        if "测试内容保持" in content_after_preserve:
            print("✅ preserve_results=True时内容保持正常")
        else:
            print("❌ preserve_results=True时内容被清空")
            return False
        
        # 测试preserve_results=False
        print("测试preserve_results=False...")
        ui.update_match_info("西甲", "皇马", "巴萨", preserve_results=False)
        
        content_after_clear = ui.result_text.get("1.0", "end-1c")
        if "测试内容保持" not in content_after_clear:
            print("✅ preserve_results=False时内容正确清空")
        else:
            print("❌ preserve_results=False时内容未清空")
            return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ UI测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试基本面分析页内容保持功能...")
    
    results = []
    
    # 运行测试
    results.append(("内容保持逻辑测试", test_preserve_logic()))
    results.append(("UI参数测试", test_ui_preserve_parameter()))
    
    # 汇总结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 所有测试通过！")
        print("✅ 基本面分析页内容保持功能已实现")
        print("✅ 标签页切换时（同一场比赛）内容会保持")
        print("✅ 选择新比赛时内容会正确清空")
        print("\n📋 功能说明:")
        print("1. 在同一场比赛中切换标签页，分析结果会保持")
        print("2. 选择新的比赛时，会清空之前的分析结果")
        print("3. 修改采用最小改动原则，只增加了preserve_results参数")
    else:
        print("⚠️  部分测试失败，请检查修改。")
    print("="*50)
    
    return all_passed

if __name__ == "__main__":
    main()
