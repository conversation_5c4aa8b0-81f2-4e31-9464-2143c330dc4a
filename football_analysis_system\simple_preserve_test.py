#!/usr/bin/env python3
"""
简单测试基本面分析页内容保持功能的代码修改
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_code_changes():
    """测试代码修改是否正确"""
    print("=== 检查基本面分析页内容保持功能的代码修改 ===")
    
    try:
        # 检查FundamentalAnalysisTab的修改
        tab_file = current_dir / "ui" / "tabs" / "fundamental_analysis_tab.py"
        ui_file = current_dir / "ui" / "tabs" / "fundamental_analysis_ui.py"
        
        if not tab_file.exists():
            print("❌ 找不到fundamental_analysis_tab.py文件")
            return False
            
        if not ui_file.exists():
            print("❌ 找不到fundamental_analysis_ui.py文件")
            return False
        
        # 检查Tab文件的修改
        with open(tab_file, 'r', encoding='utf-8') as f:
            tab_content = f.read()
        
        tab_checks = [
            ("preserve_results=is_tab_switch and is_same_match", "Tab文件中添加了preserve_results参数逻辑"),
            ("is_tab_switch=False", "Tab文件中有is_tab_switch=False的调用"),
            ("is_tab_switch=True", "Tab文件中有is_tab_switch=True的调用")
        ]
        
        tab_all_found = True
        for check_text, description in tab_checks:
            if check_text in tab_content:
                print(f"✅ {description}: 找到 '{check_text}'")
            else:
                print(f"❌ {description}: 未找到 '{check_text}'")
                tab_all_found = False
        
        # 检查UI文件的修改
        with open(ui_file, 'r', encoding='utf-8') as f:
            ui_content = f.read()
        
        ui_checks = [
            ("preserve_results=False", "UI文件中添加了preserve_results参数"),
            ("if not preserve_results:", "UI文件中添加了preserve_results条件判断"),
            ("self.clear_result()", "UI文件中保留了clear_result调用")
        ]
        
        ui_all_found = True
        for check_text, description in ui_checks:
            if check_text in ui_content:
                print(f"✅ {description}: 找到 '{check_text}'")
            else:
                print(f"❌ {description}: 未找到 '{check_text}'")
                ui_all_found = False
        
        return tab_all_found and ui_all_found
        
    except Exception as e:
        print(f"❌ 检查过程中出现错误: {e}")
        return False

def test_logic_simulation():
    """模拟测试逻辑"""
    print("\n=== 模拟测试内容保持逻辑 ===")
    
    try:
        # 模拟不同场景的参数组合
        scenarios = [
            {
                "name": "首次选择比赛",
                "is_tab_switch": False,
                "is_same_match": False,
                "expected_preserve": False,
                "description": "应该清空结果"
            },
            {
                "name": "标签页切换（同一场比赛）",
                "is_tab_switch": True,
                "is_same_match": True,
                "expected_preserve": True,
                "description": "应该保持结果"
            },
            {
                "name": "标签页切换（不同比赛）",
                "is_tab_switch": True,
                "is_same_match": False,
                "expected_preserve": False,
                "description": "应该清空结果"
            },
            {
                "name": "选择新比赛",
                "is_tab_switch": False,
                "is_same_match": False,
                "expected_preserve": False,
                "description": "应该清空结果"
            }
        ]
        
        all_correct = True
        for scenario in scenarios:
            # 模拟preserve_results的计算逻辑
            preserve_results = scenario["is_tab_switch"] and scenario["is_same_match"]
            
            if preserve_results == scenario["expected_preserve"]:
                print(f"✅ {scenario['name']}: preserve_results={preserve_results} ({scenario['description']})")
            else:
                print(f"❌ {scenario['name']}: preserve_results={preserve_results}, 期望={scenario['expected_preserve']}")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 逻辑模拟测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始检查基本面分析页内容保持功能的修改...")
    
    results = []
    
    # 运行测试
    results.append(("代码修改检查", test_code_changes()))
    results.append(("逻辑模拟测试", test_logic_simulation()))
    
    # 汇总结果
    print("\n" + "="*50)
    print("检查结果汇总:")
    print("="*50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 所有检查通过！")
        print("✅ 基本面分析页内容保持功能已正确实现")
        print("\n📋 实现说明:")
        print("1. 在FundamentalAnalysisTab中添加了preserve_results逻辑")
        print("2. 在FundamentalAnalysisUI中添加了preserve_results参数")
        print("3. 只有在preserve_results=False时才清空结果")
        print("4. 标签页切换且同一场比赛时会保持结果")
        print("5. 选择新比赛时会清空结果")
        print("\n🎯 使用效果:")
        print("- 在基本面分析页计算出信心度后")
        print("- 切换到其他标签页再回来，内容会保持")
        print("- 只有选择新比赛时才会清空重新计算")
    else:
        print("⚠️  部分检查失败，请检查修改。")
    print("="*50)
    
    return all_passed

if __name__ == "__main__":
    main()
