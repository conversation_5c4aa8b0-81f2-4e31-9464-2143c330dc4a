#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试基本面分析与历史区间分析的集成功能
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from football_analysis_system.ui.tabs.fundamental_analysis_formatter import ResultFormatter
from football_analysis_system.ui.tabs.fundamental_analysis_tab import FundamentalAnalysisTab

def test_formatter_analysis_results():
    """测试格式化器的分析结果保存功能"""
    print("测试基本面分析结果保存功能...")
    
    # 创建一个简单的UI模拟器
    class MockUI:
        def append_result(self, text):
            print(f"UI输出: {text}", end='')
        
        def append_colored_result(self, text, color=None, tag=None):
            print(f"UI彩色输出: {text}", end='')
    
    # 创建格式化器实例
    formatter = ResultFormatter(MockUI())
    formatter.set_teams("测试主队", "测试客队", "测试联赛")
    
    # 模拟评分结果
    test_ratings = {
        'home_win_score': 6.8,
        'draw_score': 4.2,
        'away_win_score': 2.8,
        'confidence_level': '信心中上'
    }
    
    # 调用保存分析结果的方法
    formatter._save_analysis_results(test_ratings)
    
    # 获取保存的结果
    results = formatter.get_latest_analysis_results()
    
    if results:
        print("✓ 分析结果保存成功!")
        print(f"主胜信心: {results['home_win']['confidence']}")
        print(f"平局信心: {results['draw']['confidence']}")
        print(f"客胜信心: {results['away_win']['confidence']}")
        
        print("\n主胜合理区间:")
        for rule in results['home_win']['odds_rules']:
            print(f"  - {rule}")
            
        print("\n平局合理区间:")
        for rule in results['draw']['odds_rules']:
            print(f"  - {rule}")
            
        print("\n客胜合理区间:")
        for rule in results['away_win']['odds_rules']:
            print(f"  - {rule}")
    else:
        print("✗ 分析结果保存失败!")
    
    return results is not None

def test_gui_integration():
    """测试GUI集成功能"""
    print("\n测试GUI集成功能...")
    
    # 创建主窗口
    root = tk.Tk()
    root.title("基本面分析集成测试")
    root.geometry("800x600")
    
    # 创建基本面分析标签页
    fundamental_tab = FundamentalAnalysisTab(root)
    fundamental_tab.pack(fill=tk.BOTH, expand=True)
    
    # 模拟分析结果
    test_ratings = {
        'home_win_score': 7.2,
        'draw_score': 3.8,
        'away_win_score': 5.1,
        'confidence_level': '信心中上'
    }
    
    # 设置队伍信息
    fundamental_tab.formatter.set_teams("皇家马德里", "巴塞罗那", "西甲")
    
    # 保存测试结果
    fundamental_tab.formatter._save_analysis_results(test_ratings)
    
    # 创建测试按钮
    def test_get_results():
        results = fundamental_tab.get_latest_analysis_results()
        if results:
            print("✓ GUI集成测试成功!")
            print(f"获取到的结果: {results['teams']['home_team']} vs {results['teams']['away_team']}")
        else:
            print("✗ GUI集成测试失败!")
    
    test_button = tk.Button(root, text="测试获取分析结果", command=test_get_results)
    test_button.pack(pady=10)
    
    # 添加说明标签
    info_label = tk.Label(root, text="点击按钮测试获取基本面分析结果功能", 
                         font=("Arial", 12))
    info_label.pack(pady=10)
    
    # 设置应用引用（模拟主应用结构）
    root.app = type('MockApp', (), {
        'fundamental_analysis_tab': fundamental_tab
    })()
    
    print("GUI测试窗口已创建，请手动测试...")
    root.mainloop()

def main():
    """主测试函数"""
    print("开始测试基本面分析与历史区间分析的集成功能...\n")
    
    # 测试1: 格式化器功能
    success1 = test_formatter_analysis_results()
    
    # 测试2: GUI集成功能
    if success1:
        print("\n基础功能测试通过，启动GUI测试...")
        test_gui_integration()
    else:
        print("\n基础功能测试失败，跳过GUI测试")

if __name__ == "__main__":
    main()
