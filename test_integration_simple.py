#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试基本面分析与历史区间分析的集成功能
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_integration():
    """测试集成功能"""
    print("开始测试基本面分析与历史区间分析的集成...")
    
    # 创建主窗口
    root = tk.Tk()
    root.title("集成测试")
    root.geometry("1200x800")
    
    # 创建notebook
    notebook = ttk.Notebook(root)
    notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # 导入必要的模块
    from football_analysis_system.ui.tabs.fundamental_analysis_tab import FundamentalAnalysisTab
    from football_analysis_system.ui.tabs.historical_odds_interval_tab import HistoricalOddsIntervalTab
    from football_analysis_system.analysis.odds_analyzer import OddsAnalyzer
    from football_analysis_system.analysis.interval_analyzer import IntervalAnalyzer
    from football_analysis_system.config import DB_STANDARD_ODDS, DB_STRENGTH_MATCHUP, DB_MATCHES
    
    # 创建基本面分析标签页
    fundamental_tab = FundamentalAnalysisTab(notebook)
    notebook.add(fundamental_tab, text="基本面分析")
    
    # 创建分析器
    odds_analyzer = OddsAnalyzer(DB_STANDARD_ODDS)
    interval_analyzer = IntervalAnalyzer(DB_STRENGTH_MATCHUP)
    
    # 创建历史区间分析标签页
    historical_tab = HistoricalOddsIntervalTab(
        notebook, 
        odds_analyzer, 
        interval_analyzer, 
        DB_MATCHES
    )
    notebook.add(historical_tab, text="历史区间分析")
    
    # 设置应用引用
    class MockApp:
        def __init__(self):
            self.fundamental_analysis_tab = fundamental_tab
    
    root.app = MockApp()
    
    # 创建测试按钮框架
    button_frame = tk.Frame(root)
    button_frame.pack(fill=tk.X, padx=10, pady=5)
    
    def simulate_analysis():
        """模拟基本面分析"""
        print("模拟基本面分析...")
        
        # 模拟评分结果
        test_ratings = {
            'home_win_score': 6.8,
            'draw_score': 4.2,
            'away_win_score': 2.8,
            'confidence_level': '信心中上'
        }
        
        # 设置队伍信息
        fundamental_tab.formatter.set_teams("皇家马德里", "巴塞罗那", "西甲")
        
        # 保存测试结果
        fundamental_tab.formatter._save_analysis_results(test_ratings)
        
        print("基本面分析模拟完成")
        
        # 测试获取结果
        results = fundamental_tab.get_latest_analysis_results()
        if results:
            print("✓ 成功获取基本面分析结果")
            print(f"主胜信心: {results['home_win']['confidence']}")
            print(f"平局信心: {results['draw']['confidence']}")
            print(f"客胜信心: {results['away_win']['confidence']}")
        else:
            print("✗ 获取基本面分析结果失败")
    
    def refresh_historical():
        """刷新历史区间分析"""
        print("刷新历史区间分析...")
        historical_tab._refresh_fundamental_analysis()
        print("刷新完成")
    
    # 创建测试按钮
    simulate_btn = tk.Button(button_frame, text="1. 模拟基本面分析", 
                           command=simulate_analysis, bg="#4CAF50", fg="white",
                           font=("Arial", 10, "bold"), padx=20, pady=5)
    simulate_btn.pack(side=tk.LEFT, padx=5)
    
    refresh_btn = tk.Button(button_frame, text="2. 刷新历史区间分析", 
                          command=refresh_historical, bg="#2196F3", fg="white",
                          font=("Arial", 10, "bold"), padx=20, pady=5)
    refresh_btn.pack(side=tk.LEFT, padx=5)
    
    # 添加说明
    info_label = tk.Label(root, text="测试步骤：1. 点击'模拟基本面分析' 2. 切换到'历史区间分析'标签页 3. 点击'获取基本面分析'按钮", 
                         font=("Arial", 10), fg="#666666")
    info_label.pack(pady=5)
    
    print("测试界面已创建，请按照说明进行测试...")
    root.mainloop()

if __name__ == "__main__":
    test_integration()
