#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
只测试UI功能
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ui():
    """测试UI功能"""
    print("开始测试UI功能...")
    
    # 创建主窗口
    root = tk.Tk()
    root.title("UI测试")
    root.geometry("1200x800")
    
    # 导入必要的模块
    from football_analysis_system.ui.tabs.historical_odds_interval_tab import HistoricalOddsIntervalTab
    from football_analysis_system.analysis.odds_analyzer import OddsAnalyzer
    from football_analysis_system.analysis.interval_analyzer import IntervalAnalyzer
    from football_analysis_system.config import DB_STANDARD_ODDS, DB_STRENGTH_MATCHUP, DB_MATCHES
    
    # 创建分析器
    odds_analyzer = OddsAnalyzer(DB_STANDARD_ODDS)
    interval_analyzer = IntervalAnalyzer(DB_STRENGTH_MATCHUP)
    
    # 创建历史区间分析标签页
    historical_tab = HistoricalOddsIntervalTab(
        root, 
        odds_analyzer, 
        interval_analyzer, 
        DB_MATCHES
    )
    historical_tab.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # 添加说明
    info_label = tk.Label(root, text="点击右侧的'显示测试数据'按钮查看基本面分析结果显示效果", 
                         font=("Arial", 12), fg="#666666")
    info_label.pack(pady=5)
    
    print("UI测试界面已创建，请手动测试...")
    root.mainloop()

if __name__ == "__main__":
    test_ui()
