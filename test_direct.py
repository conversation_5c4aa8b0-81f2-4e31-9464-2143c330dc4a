#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试基本面分析结果的保存和获取
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from football_analysis_system.ui.tabs.fundamental_analysis_formatter import ResultFormatter

def test_direct():
    """直接测试"""
    print("开始直接测试...")
    
    # 创建一个简单的UI模拟器
    class MockUI:
        def append_result(self, text):
            pass
        
        def append_colored_result(self, text, color=None, tag=None):
            pass
    
    # 创建格式化器实例
    formatter = ResultFormatter(MockUI())
    formatter.set_teams("测试主队", "测试客队", "测试联赛")
    
    print("1. 测试保存分析结果...")
    
    # 模拟评分结果
    test_ratings = {
        'home_win_score': 6.8,
        'draw_score': 4.2,
        'away_win_score': 2.8,
        'confidence_level': '信心中上'
    }
    
    # 调用保存分析结果的方法
    formatter._save_analysis_results(test_ratings)
    
    print("2. 测试获取分析结果...")
    
    # 获取保存的结果
    results = formatter.get_latest_analysis_results()
    
    if results:
        print("✓ 分析结果保存和获取成功!")
        print(f"主胜信心: {results['home_win']['confidence']}")
        print(f"平局信心: {results['draw']['confidence']}")
        print(f"客胜信心: {results['away_win']['confidence']}")
        
        print("\n主胜合理区间:")
        for rule in results['home_win']['odds_rules']:
            print(f"  - {rule}")
            
        print("\n平局合理区间:")
        for rule in results['draw']['odds_rules']:
            print(f"  - {rule}")
            
        print("\n客胜合理区间:")
        for rule in results['away_win']['odds_rules']:
            print(f"  - {rule}")
            
        print("\n队伍信息:")
        teams = results['teams']
        print(f"  主队: {teams['home_team']}")
        print(f"  客队: {teams['away_team']}")
        print(f"  联赛: {teams['league_name']}")
        
        return True
    else:
        print("✗ 分析结果保存或获取失败!")
        return False

if __name__ == "__main__":
    success = test_direct()
    if success:
        print("\n✓ 直接测试通过")
    else:
        print("\n✗ 直接测试失败")
