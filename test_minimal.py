#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化测试
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_confidence_rules():
    """测试信心级别规则"""
    print("测试信心级别规则...")
    
    from football_analysis_system.ui.tabs.fundamental_analysis_formatter import ResultFormatter
    
    # 创建一个简单的UI模拟器
    class MockUI:
        def append_result(self, text):
            pass
        
        def append_colored_result(self, text, color=None, tag=None):
            pass
    
    # 创建格式化器实例
    formatter = ResultFormatter(MockUI())
    
    # 测试获取信心级别规则
    confidence_levels = ["信心中下", "信心中庸", "信心中上"]
    
    for level in confidence_levels:
        print(f"测试信心级别: {level}")
        rules = formatter.get_confidence_odds_rules(level)
        print(f"  规则数量: {len(rules)}")
        if rules:
            print(f"  第一个规则: {rules[0]}")
        print()
    
    print("信心级别规则测试完成")

def test_save_results():
    """测试保存结果"""
    print("测试保存结果...")
    
    from football_analysis_system.ui.tabs.fundamental_analysis_formatter import ResultFormatter
    
    # 创建一个简单的UI模拟器
    class MockUI:
        def append_result(self, text):
            pass
        
        def append_colored_result(self, text, color=None, tag=None):
            pass
    
    # 创建格式化器实例
    formatter = ResultFormatter(MockUI())
    formatter.set_teams("测试主队", "测试客队", "测试联赛")
    
    # 模拟评分结果
    test_ratings = {
        'home_win_score': 6.8,
        'draw_score': 4.2,
        'away_win_score': 2.8,
        'confidence_level': '信心中上'
    }
    
    print("开始保存分析结果...")
    
    try:
        # 手动实现保存逻辑，避免调用可能有问题的方法
        home_score = test_ratings['home_win_score']
        draw_score = test_ratings['draw_score']
        away_score = test_ratings['away_win_score']
        
        # 确定各结果的信心级别
        def get_confidence_level(score):
            if score >= 9.0:
                return "极致信心"
            elif score >= 7.9:
                return "信心充足"
            elif score >= 6.8:
                return "信心中上"
            elif score >= 4.2:
                return "信心中庸"
            elif score >= 2.8:
                return "信心中下"
            elif score >= 1.4:
                return "信心不足"
            else:
                return "欠缺信心"
                
        home_confidence = get_confidence_level(home_score)
        draw_confidence = get_confidence_level(draw_score)
        away_confidence = get_confidence_level(away_score)
        
        print(f"主胜信心: {home_confidence}")
        print(f"平局信心: {draw_confidence}")
        print(f"客胜信心: {away_confidence}")
        
        # 获取各信心级别对应的赔率规则
        home_rules = formatter.get_confidence_odds_rules(home_confidence)
        draw_rules = formatter.get_confidence_odds_rules(draw_confidence)
        away_rules = formatter.get_confidence_odds_rules(away_confidence)
        
        print(f"主胜规则数量: {len(home_rules)}")
        print(f"平局规则数量: {len(draw_rules)}")
        print(f"客胜规则数量: {len(away_rules)}")
        
        # 构建分析结果数据
        analysis_results = {
            'home_win': {
                'score': home_score,
                'confidence': home_confidence,
                'odds_rules': home_rules
            },
            'draw': {
                'score': draw_score,
                'confidence': draw_confidence,
                'odds_rules': draw_rules
            },
            'away_win': {
                'score': away_score,
                'confidence': away_confidence,
                'odds_rules': away_rules
            },
            'teams': {
                'home_team': formatter.home_team,
                'away_team': formatter.away_team,
                'league_name': formatter.league_name
            }
        }
        
        # 保存到类属性中
        formatter.latest_analysis_results = analysis_results
        
        print("✓ 分析结果保存成功")
        
        # 测试获取
        results = formatter.get_latest_analysis_results()
        if results:
            print("✓ 分析结果获取成功")
            return True
        else:
            print("✗ 分析结果获取失败")
            return False
            
    except Exception as e:
        print(f"✗ 保存过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始最小化测试...")
    
    # 测试1: 信心级别规则
    test_confidence_rules()
    
    # 测试2: 保存结果
    success = test_save_results()
    
    if success:
        print("\n✓ 最小化测试通过")
    else:
        print("\n✗ 最小化测试失败")
